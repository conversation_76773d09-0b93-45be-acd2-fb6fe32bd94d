import numpy as np

# --- BLOSUM62 矩阵读取 ---
def read_blosum62(file_path):
    """
    从文件读取 BLOSUM62 评分矩阵。

    参数:
        file_path (str): BLOSUM62 矩阵文件的路径。

    返回:
        dict: 嵌套字典，表示 BLOSUM62 矩阵。例如 blosum['A']['R'] = -1。
        list: 矩阵中的氨基酸顺序。
    """
    matrix = {}
    amino_acids = []
    with open(file_path, 'r') as f:
        lines = f.readlines()
        amino_acids = lines[0].strip().split()
        for i in range(len(amino_acids)):
            amino_acids[i] = amino_acids[i] # 处理可能的空格问题

        for line in lines[1:]:
            if line.strip() == "" or line.startswith('#'): # 跳过空行和注释
                continue
            parts = line.strip().split()
            row_aa = parts[0]
            matrix[row_aa] = {}
            for i, score_str in enumerate(parts[1:]):
                if i < len(amino_acids): # 确保不会超出索引
                     matrix[row_aa][amino_acids[i]] = int(score_str)
    # 确保所有氨基酸都在列表中，特别是最后一个 '*'
    if '*' not in amino_acids and any('*' in row for row in matrix.keys()):
        amino_acids.append('*')

    # 确保矩阵是对称的并且包含所有氨基酸对
    all_aas_in_matrix = set(matrix.keys())
    for aa_list_member in amino_acids:
        all_aas_in_matrix.add(aa_list_member)

    for aa1 in all_aas_in_matrix:
        if aa1 not in matrix:
            matrix[aa1] = {}
        for aa2 in all_aas_in_matrix:
            if aa2 not in matrix[aa1]:
                # 尝试从对称位置获取，如果不存在则可能需要报错或设默认值
                if aa2 in matrix and aa1 in matrix[aa2]:
                     matrix[aa1][aa2] = matrix[aa2][aa1]
                else:
                    # 对于标准BLOSUM62，'*'与其他所有氨基酸的匹配通常是-4
                    if aa1 == '*' or aa2 == '*':
                        matrix[aa1][aa2] = -4
                    else:
                        # 对于其他缺失的非'*'配对，可以根据实际情况处理
                        # 这里暂时也设置为一个较低的值，或者抛出错误
                        # print(f"Warning: Missing score for {aa1}-{aa2}, setting to 0 or error.")
                        matrix[aa1][aa2] = 0 # 或其他默认值

    return matrix, amino_acids

# --- 辅助函数：打印矩阵 ---
def print_dp_matrix(matrix, seq1, seq2, title="动态规划评分矩阵"):
    """
    以表格形式打印动态规划评分矩阵。

    参数:
        matrix (numpy.array): 动态规划评分矩阵。
        seq1 (str): 序列1。
        seq2 (str): 序列2。
        title (str): 矩阵的标题。
    """
    print(f"\n--- {title} ---")
    # 构建表头
    header_row1 = "      " + "  ".join(list(seq1))
    header_row2 = "    " + " ".join([f"{s:2}" for s in list(seq2)])

    print("      " + "    " + "  ".join(list(seq1)))
    print("    " + "-" * (len(header_row1) - 2))

    # 打印矩阵内容
    print(f"  {matrix[0,0]:3d} |" + "".join([f"{matrix[0, j]:3d}" for j in range(1, matrix.shape[1])]))
    for i in range(1, matrix.shape[0]):
        row_str = f"{seq2[i-1]} {matrix[i,0]:3d} |"
        for j in range(1, matrix.shape[1]):
            row_str += f"{matrix[i,j]:3d}"
        print(row_str)
    print("-" * len(header_row1))


# --- 全局比对：Needleman-Wunsch ---
def needleman_wunsch(seq1, seq2, blosum_matrix, gap_penalty):
    """
    使用 Needleman-Wunsch 算法进行全局比对。

    参数:
        seq1 (str): 第一条蛋白质序列。
        seq2 (str): 第二条蛋白质序列。
        blosum_matrix (dict): BLOSUM62 评分矩阵。
        gap_penalty (int): 线性空缺罚分。

    返回:
        tuple: (比对后的序列1, 比对后的序列2, 总分, 动态规划矩阵)
    """
    n = len(seq1)
    m = len(seq2)

    # 初始化动态规划矩阵
    dp_matrix = np.zeros((m + 1, n + 1), dtype=int)

    # 初始化第一行和第一列（空缺罚分）
    for i in range(m + 1):
        dp_matrix[i][0] = i * gap_penalty
    for j in range(n + 1):
        dp_matrix[0][j] = j * gap_penalty

    # 填充动态规划矩阵
    for i in range(1, m + 1):
        for j in range(1, n + 1):
            match_score = blosum_matrix[seq2[i-1]][seq1[j-1]]
            score_diag = dp_matrix[i-1][j-1] + match_score
            score_up = dp_matrix[i-1][j] + gap_penalty
            score_left = dp_matrix[i][j-1] + gap_penalty
            dp_matrix[i][j] = max(score_diag, score_up, score_left)

    # 回溯以获得最优比对
    align1 = ""
    align2 = ""
    i, j = m, n
    total_score = dp_matrix[i][j]

    while i > 0 or j > 0:
        current_score = dp_matrix[i][j]
        # 检查对角线
        if i > 0 and j > 0 and current_score == dp_matrix[i-1][j-1] + blosum_matrix[seq2[i-1]][seq1[j-1]]:
            align1 = seq1[j-1] + align1
            align2 = seq2[i-1] + align2
            i -= 1
            j -= 1
        # 检查上方（seq2中的空缺）
        elif i > 0 and current_score == dp_matrix[i-1][j] + gap_penalty:
            align1 = "-" + align1
            align2 = seq2[i-1] + align2
            i -= 1
        # 检查左方（seq1中的空缺）
        elif j > 0 and current_score == dp_matrix[i][j-1] + gap_penalty:
            align1 = seq1[j-1] + align1
            align2 = "-" + align2
            j -= 1
        else: # 处理 i=0 或 j=0 的情况，或者在路径不明确时优先对角线（如果前面逻辑已覆盖，此else可简化）
            if i == 0 and j > 0 : # 只能从左边来
                 align1 = seq1[j-1] + align1
                 align2 = "-" + align2
                 j -=1
            elif j == 0 and i > 0: # 只能从上边来
                 align1 = "-" + align1
                 align2 = seq2[i-1] + align2
                 i -=1
            else: # 如果 i=0, j=0，则停止
                break


    return align1, align2, total_score, dp_matrix

# --- 局部比对：Smith-Waterman ---
def smith_waterman(seq1, seq2, blosum_matrix, gap_penalty):
    """
    使用 Smith-Waterman 算法进行局部比对。

    参数:
        seq1 (str): 第一条蛋白质序列。
        seq2 (str): 第二条蛋白质序列。
        blosum_matrix (dict): BLOSUM62 评分矩阵。
        gap_penalty (int): 线性空缺罚分。

    返回:
        tuple: (比对后的序列1, 比对后的序列2, 最高分, 起始位置(i,j), 终止位置(i,j), 动态规划矩阵)
               如果最高分为0，则返回 ("", "", 0, None, None, dp_matrix)
    """
    n = len(seq1)
    m = len(seq2)

    dp_matrix = np.zeros((m + 1, n + 1), dtype=int)
    max_score = 0
    max_pos = (0, 0)  # (row, col) 对应 (seq2_idx+1, seq1_idx+1)

    # 填充动态规划矩阵
    for i in range(1, m + 1):
        for j in range(1, n + 1):
            match_score = blosum_matrix[seq2[i-1]][seq1[j-1]]
            score_diag = dp_matrix[i-1][j-1] + match_score
            score_up = dp_matrix[i-1][j] + gap_penalty
            score_left = dp_matrix[i][j-1] + gap_penalty
            current_score = max(0, score_diag, score_up, score_left) # Smith-Waterman核心：不小于0
            dp_matrix[i][j] = current_score

            if current_score > max_score:
                max_score = current_score
                max_pos = (i, j)

    if max_score == 0:
        return "", "", 0, None, None, dp_matrix

    # 回溯以获得局部最优比对
    align1 = ""
    align2 = ""
    i, j = max_pos
    end_pos = (i -1 , j - 1) # 记录序列中的实际结束索引

    while dp_matrix[i][j] > 0:
        current_score_val = dp_matrix[i][j]
        # 检查对角线
        diag_prev_score = dp_matrix[i-1][j-1]
        if current_score_val == diag_prev_score + blosum_matrix[seq2[i-1]][seq1[j-1]]:
            align1 = seq1[j-1] + align1
            align2 = seq2[i-1] + align2
            i -= 1
            j -= 1
        # 检查上方（seq2中的空缺）
        elif current_score_val == dp_matrix[i-1][j] + gap_penalty:
            align1 = "-" + align1
            align2 = seq2[i-1] + align2
            i -= 1
        # 检查左方（seq1中的空缺）
        elif current_score_val == dp_matrix[i][j-1] + gap_penalty:
            align1 = seq1[j-1] + align1
            align2 = "-" + align2
            j -= 1
        else: # 如果没有明确的路径导致当前分数（这理论上不应该发生，除非gap_penalty为0且有多个0路径）
              # 或者当分数降为0时，我们优先匹配（如果可以）
            if current_score_val > 0 : #确保我们只在分数大于0时进行回溯
                # 这种路径不应该发生，除非是特殊情况或错误
                # print(f"Warning: Ambiguous traceback or error at ({i},{j}) with score {current_score_val}")
                # 强制匹配，如果可能，否则停止
                if i > 0 and j > 0 :
                    align1 = seq1[j-1] + align1
                    align2 = seq2[i-1] + align2
                    i -= 1
                    j -= 1
                else:
                    break # 到达边界
            else:
                 break


    start_pos = (i, j) # 记录序列中的实际开始索引 (回溯结束后i,j指向比对开始的前一个位置)

    return align1, align2, max_score, start_pos, end_pos, dp_matrix


# --- 一致性计算与可视化 ---
def calculate_consistency(align1, align2):
    """
    计算两条比对序列的一致性百分比。

    参数:
        align1 (str): 比对后的序列1。
        align2 (str): 比对后的序列2。

    返回:
        float: 一致性百分比。
    """
    if len(align1) != len(align2):
        raise ValueError("比对后的序列长度必须相同。")
    if not align1: # 如果序列为空
        return 0.0

    matches = 0
    for char1, char2 in zip(align1, align2):
        if char1 == char2 and char1 != '-': # 匹配且不是两个空位对齐
            matches += 1
    
    alignment_length = len(align1) # 总比对长度
    return (matches / alignment_length) * 100 if alignment_length > 0 else 0.0

def generate_dot_plot(seq1, seq2, window_size=1):
    """
    生成点阵图。

    参数:
        seq1 (str): 序列1 (原始序列，非比对后)。
        seq2 (str): 序列2 (原始序列，非比对后)。
        window_size (int): 用于过滤噪声的窗口大小。
                           在窗口内匹配达到一定阈值（这里简化为窗口内完全匹配）才标记。
                           对于简单的 '*' 表示匹配，window_size=1。
    
    输出:
        打印点阵图到控制台。
    """
    print("\n--- 点阵图 (Dot Plot) ---")
    print(f"窗口大小: {window_size}")
    
    # 打印列标 (seq1)
    print("    " + " ".join(list(seq1)))
    print("   " + "-" * (len(seq1) * 2))

    for i in range(len(seq2) - window_size + 1):
        row_char = seq2[i] if window_size == 1 else seq2[i:i+window_size][0] # 显示窗口的第一个字符作为行标
        row_display = f"{row_char} | "
        for j in range(len(seq1) - window_size + 1):
            match = True
            if window_size > 1:
                sub_seq1 = seq1[j : j + window_size]
                sub_seq2 = seq2[i : i + window_size]
                if sub_seq1 != sub_seq2:
                    match = False
            else: # window_size == 1
                if seq1[j] != seq2[i]:
                    match = False
            
            row_display += "* " if match else "  "
        print(row_display)
    print("-" * (len(seq1) * 2 + 3))


# --- 测试用例 ---
def run_tests():
    """运行测试用例"""
    print("开始运行测试用例...")

    
    
    print("=================================================task1======================================================================")
    
    
    # 示例序列和参数
    seq1_global = "PLEASANTLY"
    seq2_global = "MEANLY"


    seq1_local = "PAWHEAE"
    seq2_local = "HEAGAWGHEE"


    gap_penalty = -8
    blosum_file = r"D:\DeskTop\鲁东大学作业\周树森\平时实践\2024120864_解鸿港\code\data\blosum62.txt"

    try:
        blosum_matrix, amino_acids = read_blosum62(blosum_file)

    except FileNotFoundError:
        print(f"错误: BLOSUM62 矩阵文件 '{blosum_file}' 未找到。请确保文件路径正确。")
        print("请将 'blosum62.txt' 文件放在脚本所在目录的 'data' 子目录下，或者修改脚本中的路径。")
        # 创建一个虚拟的BLOSUM62矩阵用于后续代码的运行，实际应用中应确保文件存在
        amino_acids_default = ['A', 'R', 'N', 'D', 'C', 'Q', 'E', 'G', 'H', 'I', 'L', 'K', 'M', 'F', 'P', 'S', 'T', 'W', 'Y', 'V', 'B', 'Z', 'X', '*']
        blosum_matrix = {aa1: {aa2: 1 if aa1 == aa2 else -1 for aa2 in amino_acids_default} for aa1 in amino_acids_default}
        print("警告: 使用了简化的虚拟BLOSUM矩阵。")


    # --- 测试 Needleman-Wunsch ---
    print("测试全局比对 (Needleman-Wunsch)")
    print(f"序列1: {seq1_global}")
    print(f"序列2: {seq2_global}")
    print(f"空缺罚分: {gap_penalty}")
    
    nw_align1, nw_align2, nw_score, nw_dp_matrix = needleman_wunsch(seq1_global, seq2_global, blosum_matrix, gap_penalty)
    
    print_dp_matrix(nw_dp_matrix, seq1_global, seq2_global, title="Needleman-Wunsch 评分矩阵")
    print("\n最优全局比对:")
    print(f"序列1: {nw_align1}")
    print(f"序列2: {nw_align2}")
    print(f"总分: {nw_score}")



    print("=================================================task2======================================================================")

    # --- 测试 Smith-Waterman ---
    print("测试局部比对 (Smith-Waterman)")
    print(f"序列1: {seq1_local}")
    print(f"序列2: {seq2_local}")
    print(f"空缺罚分: {gap_penalty}")

    sw_align1, sw_align2, sw_score, sw_start, sw_end, sw_dp_matrix = smith_waterman(seq1_local, seq2_local, blosum_matrix, gap_penalty)
    
    # 为了简化输出，这里传入原始序列用于打印dp_matrix的表头
    print_dp_matrix(sw_dp_matrix, seq1_local, seq2_local, title="Smith-Waterman 评分矩阵")
    
    if sw_score > 0:
        print("\n局部最优比对:")
        print(f"序列1: {sw_align1}")
        print(f"序列2: {sw_align2}")
        print(f"分值: {sw_score}")
        # sw_start 和 sw_end 是dp_matrix中的索引(回溯后的)，需要转换为0-based的序列索引
        # start_pos_seq1 = sw_start[1]
        # start_pos_seq2 = sw_start[0]
        # end_pos_seq1 = sw_end[1]
        # end_pos_seq2 = sw_end[0]
        # print(f"起始位置 (seq1_idx, seq2_idx): ({start_pos_seq1}, {start_pos_seq2})")
        # print(f"终止位置 (seq1_idx, seq2_idx): ({end_pos_seq1}, {end_pos_seq2})")

        # 打印比对在原始序列中的精确位置 (0-indexed)
        # 注意：Smith-Waterman回溯结束后的sw_start是比对开始字符在原序列中的索引+1 （因为dp表有0行0列）
        # 而sw_end是比对结束字符在原序列中的索引。
        # 例子: seq1="AXA", seq2="AYLA", 比对 "A-A" vs "AYA"
        # dp_matrix max_pos (r,c) -> seq2[r-1], seq1[c-1]
        # 回溯结束后 (i,j) -> seq2[i-1], seq1[j-1] 是比对开始的前一个字符
        # 所以比对开始于 seq2[i], seq1[j]
        print(f"比对起始于 序列1索引: {sw_start[1]}, 序列2索引: {sw_start[0]}")
        print(f"比对终止于 序列1索引: {sw_end[1]}, 序列2索引: {sw_end[0]}")


    else:
        print("未找到分值大于0的局部比对。")


    print("=================================================task3======================================================================")

    # --- 测试一致性计算 ---
    print("\n=======================================")
    print("测试一致性计算 (使用Needleman-Wunsch结果)")
    if nw_align1 and nw_align2:
        consistency_nw = calculate_consistency(nw_align1, nw_align2)
        print(f"Needleman-Wunsch 比对一致性: {consistency_nw:.2f}%")
    else:
        print("Needleman-Wunsch 未产生有效比对，跳过一致性计算。")

    print("\n测试一致性计算 (使用Smith-Waterman结果)")
    if sw_align1 and sw_align2 and sw_score > 0:
        consistency_sw = calculate_consistency(sw_align1, sw_align2)
        print(f"Smith-Waterman 比对一致性: {consistency_sw:.2f}%")
    else:
        print("Smith-Waterman 未产生有效比对或分数为0，跳过一致性计算。")

    # --- 测试点阵图 ---
    # 使用较短的序列进行点阵图演示，以免输出过长
    dot_plot_seq1 = "AGCTAGCT"
    dot_plot_seq2 = "AGCTGGCT"
    print("\n=======================================")
    print("测试点阵图")
    print(f"序列1: {dot_plot_seq1}")
    print(f"序列2: {dot_plot_seq2}")
    generate_dot_plot(dot_plot_seq1, dot_plot_seq2, window_size=1)
    generate_dot_plot(dot_plot_seq1, dot_plot_seq2, window_size=2)
    
    dot_plot_seq3 = "PLEASANTLY"
    dot_plot_seq4 = "MEANLY"
    print(f"\n序列1: {dot_plot_seq3}")
    print(f"序列2: {dot_plot_seq4}")
    generate_dot_plot(dot_plot_seq3, dot_plot_seq4, window_size=1)


if __name__ == "__main__":
    run_tests() 