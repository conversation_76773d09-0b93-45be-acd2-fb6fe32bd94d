#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生物信息学蛋白质序列比对工具包 2.0
实现全局比对(<PERSON><PERSON>an-<PERSON><PERSON><PERSON>)、局部比对(<PERSON>-<PERSON>)和一致性分析

作者: 解鸿港
学号: 2024120864
"""

import numpy as np


class ProteinAlignmentToolkit:
    """蛋白质序列比对工具包类"""

    def __init__(self, blosum_file_path=None):
        """
        初始化比对工具包

        参数:
            blosum_file_path (str): BLOSUM62矩阵文件路径
        """
        self.blosum_matrix = None
        self.amino_acids = None

        if blosum_file_path:
            self.load_blosum62(blosum_file_path)

    def load_blosum62(self, file_path):
        """
        从文件读取BLOSUM62评分矩阵

        参数:
            file_path (str): BLOSUM62矩阵文件的路径

        返回:
            bool: 是否成功加载矩阵
        """
        try:
            matrix = {}
            amino_acids = []

            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()

                # 读取氨基酸列表（第一行）
                amino_acids = lines[0].strip().split()

                # 读取矩阵数据
                for line in lines[1:]:
                    if line.strip() == "" or line.startswith('#'):
                        continue

                    parts = line.strip().split()
                    row_aa = parts[0]
                    matrix[row_aa] = {}

                    for i, score_str in enumerate(parts[1:]):
                        if i < len(amino_acids):
                            matrix[row_aa][amino_acids[i]] = int(score_str)

            # 确保矩阵对称性
            self._ensure_matrix_symmetry(matrix, amino_acids)

            self.blosum_matrix = matrix
            self.amino_acids = amino_acids
            print(f"成功加载BLOSUM62矩阵，包含{len(amino_acids)}种氨基酸")
            return True

        except FileNotFoundError:
            print(f"错误: 找不到BLOSUM62矩阵文件 '{file_path}'")
            return False
        except Exception as e:
            print(f"加载BLOSUM62矩阵时发生错误: {e}")
            return False

    def _ensure_matrix_symmetry(self, matrix, amino_acids):
        """确保BLOSUM矩阵的对称性和完整性"""
        all_aas = set(matrix.keys()) | set(amino_acids)

        for aa1 in all_aas:
            if aa1 not in matrix:
                matrix[aa1] = {}
            for aa2 in all_aas:
                if aa2 not in matrix[aa1]:
                    if aa2 in matrix and aa1 in matrix[aa2]:
                        matrix[aa1][aa2] = matrix[aa2][aa1]
                    else:
                        # 对于缺失的配对，设置默认值
                        if aa1 == '*' or aa2 == '*':
                            matrix[aa1][aa2] = -4
                        else:
                            matrix[aa1][aa2] = 0

    def print_dp_matrix(self, matrix, seq1, seq2, title="动态规划评分矩阵"):
        """
        以表格形式打印动态规划评分矩阵

        参数:
            matrix (numpy.array): 动态规划评分矩阵
            seq1 (str): 序列1
            seq2 (str): 序列2
            title (str): 矩阵标题
        """
        print(f"\n--- {title} ---")

        # 打印列标题（seq1）
        header = "      " + "".join([f"{c:4}" for c in seq1])
        print(header)
        print("    " + "-" * (len(header) - 4))

        # 打印第一行（初始化行）
        first_row = f"  {matrix[0,0]:3d} |"
        for j in range(1, matrix.shape[1]):
            first_row += f"{matrix[0,j]:3d} "
        print(first_row)

        # 打印其余行
        for i in range(1, matrix.shape[0]):
            row_str = f"{seq2[i-1]} {matrix[i,0]:3d} |"
            for j in range(1, matrix.shape[1]):
                row_str += f"{matrix[i,j]:3d} "
            print(row_str)

        print("-" * len(header))

    def needleman_wunsch(self, seq1, seq2, gap_penalty=-8):
        """
        使用Needleman-Wunsch算法进行全局比对

        参数:
            seq1 (str): 第一条蛋白质序列
            seq2 (str): 第二条蛋白质序列
            gap_penalty (int): 线性空缺罚分

        返回:
            tuple: (比对后的序列1, 比对后的序列2, 总分, 动态规划矩阵)
        """
        if not self.blosum_matrix:
            raise ValueError("请先加载BLOSUM62矩阵")

        n, m = len(seq1), len(seq2)

        # 初始化动态规划矩阵
        dp_matrix = np.zeros((m + 1, n + 1), dtype=int)

        # 初始化边界条件
        for i in range(m + 1):
            dp_matrix[i][0] = i * gap_penalty
        for j in range(n + 1):
            dp_matrix[0][j] = j * gap_penalty

        # 填充动态规划矩阵
        for i in range(1, m + 1):
            for j in range(1, n + 1):
                match_score = self.blosum_matrix[seq2[i-1]][seq1[j-1]]
                score_diag = dp_matrix[i-1][j-1] + match_score
                score_up = dp_matrix[i-1][j] + gap_penalty
                score_left = dp_matrix[i][j-1] + gap_penalty
                dp_matrix[i][j] = max(score_diag, score_up, score_left)

        # 回溯获得最优比对
        align1, align2 = self._traceback_global(dp_matrix, seq1, seq2, gap_penalty)
        total_score = dp_matrix[m][n]

        return align1, align2, total_score, dp_matrix

    def _traceback_global(self, dp_matrix, seq1, seq2, gap_penalty):
        """全局比对的回溯算法"""
        align1, align2 = "", ""
        i, j = len(seq2), len(seq1)

        while i > 0 or j > 0:
            current_score = dp_matrix[i][j]

            # 检查对角线方向
            if (i > 0 and j > 0 and
                current_score == dp_matrix[i-1][j-1] + self.blosum_matrix[seq2[i-1]][seq1[j-1]]):
                align1 = seq1[j-1] + align1
                align2 = seq2[i-1] + align2
                i -= 1
                j -= 1
            # 检查上方（seq2中插入空缺）
            elif i > 0 and current_score == dp_matrix[i-1][j] + gap_penalty:
                align1 = "-" + align1
                align2 = seq2[i-1] + align2
                i -= 1
            # 检查左方（seq1中插入空缺）
            elif j > 0 and current_score == dp_matrix[i][j-1] + gap_penalty:
                align1 = seq1[j-1] + align1
                align2 = "-" + align2
                j -= 1
            else:
                # 处理边界情况
                if i == 0 and j > 0:
                    align1 = seq1[j-1] + align1
                    align2 = "-" + align2
                    j -= 1
                elif j == 0 and i > 0:
                    align1 = "-" + align1
                    align2 = seq2[i-1] + align2
                    i -= 1
                else:
                    break

        return align1, align2

    def smith_waterman(self, seq1, seq2, gap_penalty=-8):
        """
        使用Smith-Waterman算法进行局部比对

        参数:
            seq1 (str): 第一条蛋白质序列
            seq2 (str): 第二条蛋白质序列
            gap_penalty (int): 线性空缺罚分

        返回:
            tuple: (比对后的序列1, 比对后的序列2, 最高分, 起始位置, 终止位置, 动态规划矩阵)
        """
        if not self.blosum_matrix:
            raise ValueError("请先加载BLOSUM62矩阵")

        n, m = len(seq1), len(seq2)

        # 初始化动态规划矩阵
        dp_matrix = np.zeros((m + 1, n + 1), dtype=int)
        max_score = 0
        max_pos = (0, 0)

        # 填充动态规划矩阵
        for i in range(1, m + 1):
            for j in range(1, n + 1):
                match_score = self.blosum_matrix[seq2[i-1]][seq1[j-1]]
                score_diag = dp_matrix[i-1][j-1] + match_score
                score_up = dp_matrix[i-1][j] + gap_penalty
                score_left = dp_matrix[i][j-1] + gap_penalty

                # Smith-Waterman核心：分数不能小于0
                current_score = max(0, score_diag, score_up, score_left)
                dp_matrix[i][j] = current_score

                if current_score > max_score:
                    max_score = current_score
                    max_pos = (i, j)

        if max_score == 0:
            return "", "", 0, None, None, dp_matrix

        # 回溯获得局部最优比对
        align1, align2, start_pos, end_pos = self._traceback_local(
            dp_matrix, seq1, seq2, max_pos, gap_penalty)

        return align1, align2, max_score, start_pos, end_pos, dp_matrix

    def _traceback_local(self, dp_matrix, seq1, seq2, max_pos, gap_penalty):
        """局部比对的回溯算法"""
        align1, align2 = "", ""
        i, j = max_pos
        end_pos = (i - 1, j - 1)  # 序列中的实际结束索引

        while dp_matrix[i][j] > 0:
            current_score = dp_matrix[i][j]

            # 检查对角线方向
            if (i > 0 and j > 0 and
                current_score == dp_matrix[i-1][j-1] + self.blosum_matrix[seq2[i-1]][seq1[j-1]]):
                align1 = seq1[j-1] + align1
                align2 = seq2[i-1] + align2
                i -= 1
                j -= 1
            # 检查上方（seq2中插入空缺）
            elif i > 0 and current_score == dp_matrix[i-1][j] + gap_penalty:
                align1 = "-" + align1
                align2 = seq2[i-1] + align2
                i -= 1
            # 检查左方（seq1中插入空缺）
            elif j > 0 and current_score == dp_matrix[i][j-1] + gap_penalty:
                align1 = seq1[j-1] + align1
                align2 = "-" + align2
                j -= 1
            else:
                break

        start_pos = (i, j)  # 比对开始位置
        return align1, align2, start_pos, end_pos

    def calculate_consistency(self, align1, align2):
        """
        计算两条比对序列的一致性百分比

        参数:
            align1 (str): 比对后的序列1
            align2 (str): 比对后的序列2

        返回:
            float: 一致性百分比
        """
        if len(align1) != len(align2):
            raise ValueError("比对后的序列长度必须相同")

        if not align1:
            return 0.0

        matches = 0
        for char1, char2 in zip(align1, align2):
            if char1 == char2 and char1 != '-':
                matches += 1

        return (matches / len(align1)) * 100 if len(align1) > 0 else 0.0

    def generate_dot_plot(self, seq1, seq2, window_size=1):
        """
        生成点阵图

        参数:
            seq1 (str): 序列1（原始序列，非比对后）
            seq2 (str): 序列2（原始序列，非比对后）
            window_size (int): 窗口大小，用于过滤噪声
        """
        print(f"\n--- 点阵图 (Dot Plot) ---")
        print(f"序列1: {seq1}")
        print(f"序列2: {seq2}")
        print(f"窗口大小: {window_size}")

        # 打印列标题（seq1）
        print("    " + " ".join(list(seq1)))
        print("   " + "-" * (len(seq1) * 2))

        for i in range(len(seq2) - window_size + 1):
            row_char = seq2[i] if window_size == 1 else seq2[i:i+window_size][0]
            row_display = f"{row_char} | "

            for j in range(len(seq1) - window_size + 1):
                match = True
                if window_size > 1:
                    sub_seq1 = seq1[j:j + window_size]
                    sub_seq2 = seq2[i:i + window_size]
                    if sub_seq1 != sub_seq2:
                        match = False
                else:
                    if seq1[j] != seq2[i]:
                        match = False

                row_display += "* " if match else "  "

            print(row_display)

        print("-" * (len(seq1) * 2 + 3))


def run_tests():
    """运行测试用例"""
    print("=" * 80)
    print("生物信息学蛋白质序列比对工具包 2.0 - 测试用例")
    print("=" * 80)

    # 初始化工具包
    blosum_file = "data/blosum62.txt"
    toolkit = ProteinAlignmentToolkit()

    # 尝试加载BLOSUM62矩阵
    if not toolkit.load_blosum62(blosum_file):
        print("警告: 使用简化的虚拟BLOSUM矩阵进行测试")
        # 创建简化的BLOSUM矩阵用于测试
        amino_acids = ['A', 'R', 'N', 'D', 'C', 'Q', 'E', 'G', 'H', 'I',
                      'L', 'K', 'M', 'F', 'P', 'S', 'T', 'W', 'Y', 'V',
                      'B', 'Z', 'X', '*']
        toolkit.blosum_matrix = {
            aa1: {aa2: 2 if aa1 == aa2 else -1 for aa2 in amino_acids}
            for aa1 in amino_acids
        }
        toolkit.amino_acids = amino_acids

    # 测试序列
    seq1_global = "PLEASANTLY"
    seq2_global = "MEANLY"
    seq1_local = "PAWHEAE"
    seq2_local = "HEAGAWGHEE"
    gap_penalty = -8

    print("\n" + "=" * 50)
    print("任务1: 全局比对 (Needleman-Wunsch)")
    print("=" * 50)
    print(f"序列1: {seq1_global}")
    print(f"序列2: {seq2_global}")
    print(f"空缺罚分: {gap_penalty}")

    try:
        nw_align1, nw_align2, nw_score, nw_dp_matrix = toolkit.needleman_wunsch(
            seq1_global, seq2_global, gap_penalty)

        toolkit.print_dp_matrix(nw_dp_matrix, seq1_global, seq2_global,
                               "Needleman-Wunsch 评分矩阵")

        print("\n最优全局比对:")
        print(f"序列1: {nw_align1}")
        print(f"序列2: {nw_align2}")
        print(f"总分: {nw_score}")

    except Exception as e:
        print(f"全局比对测试失败: {e}")

    print("\n" + "=" * 50)
    print("任务2: 局部比对 (Smith-Waterman)")
    print("=" * 50)
    print(f"序列1: {seq1_local}")
    print(f"序列2: {seq2_local}")
    print(f"空缺罚分: {gap_penalty}")

    try:
        sw_align1, sw_align2, sw_score, sw_start, sw_end, sw_dp_matrix = toolkit.smith_waterman(
            seq1_local, seq2_local, gap_penalty)

        toolkit.print_dp_matrix(sw_dp_matrix, seq1_local, seq2_local,
                               "Smith-Waterman 评分矩阵")

        if sw_score > 0:
            print("\n局部最优比对:")
            print(f"序列1: {sw_align1}")
            print(f"序列2: {sw_align2}")
            print(f"分值: {sw_score}")
            print(f"比对起始于 序列1索引: {sw_start[1]}, 序列2索引: {sw_start[0]}")
            print(f"比对终止于 序列1索引: {sw_end[1]}, 序列2索引: {sw_end[0]}")
        else:
            print("未找到分值大于0的局部比对")

    except Exception as e:
        print(f"局部比对测试失败: {e}")

    print("\n" + "=" * 50)
    print("任务3: 一致性计算与可视化")
    print("=" * 50)

    # 一致性计算
    try:
        if 'nw_align1' in locals() and nw_align1:
            consistency_nw = toolkit.calculate_consistency(nw_align1, nw_align2)
            print(f"Needleman-Wunsch 比对一致性: {consistency_nw:.2f}%")

        if 'sw_align1' in locals() and sw_align1 and sw_score > 0:
            consistency_sw = toolkit.calculate_consistency(sw_align1, sw_align2)
            print(f"Smith-Waterman 比对一致性: {consistency_sw:.2f}%")

    except Exception as e:
        print(f"一致性计算失败: {e}")

    # 点阵图测试
    print("\n点阵图测试:")
    dot_seq1 = "AGCTAGCT"
    dot_seq2 = "AGCTGGCT"

    try:
        toolkit.generate_dot_plot(dot_seq1, dot_seq2, window_size=1)
        toolkit.generate_dot_plot(dot_seq1, dot_seq2, window_size=2)

        # 使用测试序列生成点阵图
        toolkit.generate_dot_plot(seq1_global, seq2_global, window_size=1)

    except Exception as e:
        print(f"点阵图生成失败: {e}")

    print("\n" + "=" * 80)
    print("测试完成")
    print("=" * 80)


if __name__ == "__main__":
    run_tests()